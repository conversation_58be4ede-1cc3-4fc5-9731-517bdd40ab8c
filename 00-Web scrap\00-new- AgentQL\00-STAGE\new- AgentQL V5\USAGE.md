# دليل الاستخدام - أداة استخراج البيانات من المواقع

## 🚀 التشغيل السريع

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق
```bash
python run.py
```
أو
```bash
python app.py
```

### 3. فتح التطبيق
افتح المتصفح وانتقل إلى: `http://127.0.0.1:5000`

## 📋 كيفية الاستخدام

### الخطوة 1: إدخال رابط الموقع
```
https://example.com/movies
```

### الخطوة 2: كتابة الاستعلام
```sql
SELECT title, link, image FROM page WHERE class="movie-card"
```

### الخطوة 3: تحديد الفلاتر
- **نوع المحتوى**: اختر بين أفلام، مسلسلات، أو الكل
- **نوع العنصر**: اختر بين روابط، نصوص، صور، أو الكل

### الخطوة 4: إعداد الصفحات (اختياري)
- أدخل روابط الصفحات الأولى والثانية والثالثة
- حدد عدد الصفحات المستهدفة
- اضغط "تحليل نمط الصفحات" لاكتشاف النمط التلقائي

### الخطوة 5: بدء الاستخراج
اضغط "بدء الاستخراج" لجلب البيانات

## 🔧 أمثلة على الاستعلامات

### استخراج الأفلام
```sql
SELECT title, link, image FROM page WHERE class="movie-item"
```

### استخراج المسلسلات
```sql
SELECT title, link FROM page WHERE class="series-card"
```

### استخراج الروابط فقط
```sql
SELECT link FROM page WHERE tag="a"
```

### استخراج بشروط متعددة
```sql
SELECT title, link, image FROM page WHERE class="content-item"
```

## 🛠️ أدوات التصحيح

### مصحح الأخطاء
- اضغط "تصحيح الاستعلام" لرؤية العناصر المطابقة
- يعرض الكود المستخرج مع تمييز العناصر
- يوضح عدد العناصر المطابقة

### تحليل أنماط الصفحات
- أدخل 2-3 روابط صفحات متتالية
- اضغط "تحليل نمط الصفحات"
- سيتم اكتشاف النمط وتوليد الصفحات التالية تلقائياً

## 📊 التصدير

### تصدير JSON
- اضغط "تصدير النتائج" بعد الاستخراج
- حدد العدد الأقصى لكل ملف
- سيتم تقسيم البيانات على ملفات متعددة إذا لزم الأمر

### تنسيق الملفات المصدرة

**للأفلام:**
```json
{
  "movies_info": [
    {
      "movies_name": "اسم الفيلم",
      "movies_img": "رابط الصورة",
      "movies_href": "رابط الفيلم"
    }
  ]
}
```

**للمسلسلات:**
```json
{
  "series_info": [
    {
      "series_name": "اسم المسلسل",
      "series_img": "رابط الصورة",
      "series_href": "رابط المسلسل"
    }
  ]
}
```

## 🔍 الحقول المدعومة

- `title` / `name` / `text` - النصوص
- `link` / `href` / `url` - الروابط
- `image` / `img` / `src` - الصور
- `class` - فئات CSS
- `id` - معرفات العناصر

## 📝 الشروط المدعومة

- `class="اسم_الفئة"` - البحث بفئة CSS
- `id="معرف_العنصر"` - البحث بالمعرف
- `tag="اسم_العنصر"` - البحث بنوع العنصر

## 🧪 الاختبار

### اختبار سريع
```bash
python simple_test.py
```

### اختبار شامل
```bash
python test_app.py
```

## ⚠️ ملاحظات مهمة

1. **احترام شروط الاستخدام**: تأكد من احترام شروط استخدام المواقع المستهدفة
2. **السرعة**: لا تفرط في السرعة لتجنب حظر IP
3. **الأخلاقيات**: استخدم الأداة للأغراض التعليمية والبحثية فقط
4. **الأمان**: لا تستخرج معلومات حساسة أو شخصية

## 🆘 حل المشاكل الشائعة

### التطبيق لا يعمل
```bash
# تحقق من تثبيت المكتبات
pip install -r requirements.txt

# تشغيل التطبيق
python run.py
```

### خطأ في الاستعلام
- تأكد من صحة صيغة الاستعلام
- استخدم مصحح الأخطاء لرؤية العناصر المطابقة

### لا توجد نتائج
- تحقق من صحة الرابط
- تأكد من وجود العناصر المطلوبة في الصفحة
- جرب استعلام أبسط

### مشاكل في التصدير
- تأكد من وجود مجلد `exports`
- تحقق من صلاحيات الكتابة

## 📞 الدعم

إذا واجهت أي مشاكل:
1. راجع هذا الدليل
2. جرب الاختبارات البسيطة
3. تحقق من ملفات السجل
4. أعد تشغيل التطبيق

---

**استمتع باستخدام أداة استخراج البيانات! 🕷️**
