# نظام الإحصائيات العامة الجديد

## 🎯 التحديث المطبق

تم تغيير نظام الإحصائيات من "إحصائيات الفلترة المفصلة" إلى "الإحصائيات العامة" مع الميزات التالية:

### 1. **الإحصائيات العامة**
- ✅ **إجمالي المستخرج**: العدد الكامل المستخرج من جميع العمليات
- ✅ **المحافظة على البيانات**: لا يتم حذف الإجمالي عند الفلترة
- ✅ **البقاء حتى الإغلاق**: البيانات تبقى حتى إغلاق التطبيق أو تحديثه

### 2. **تفاصيل الفلترة**
- ✅ **عدد المستبعد بالفلتر**: العناصر التي تم استبعادها
- ✅ **عدد المتبقي بعد الفلتر**: العناصر المتبقية بعد الفلترة
- ✅ **إظهار عند الحاجة**: تظهر فقط عند تطبيق فلاتر

### 3. **تصدير محسن**
- ✅ **تصدير الإجمالي**: CSV و JSON للعدد الكامل المستخرج
- ✅ **تصدير المفلتر**: CSV و JSON للنتائج بعد الفلترة
- ✅ **نفس صيغة JSON**: الهيكل الحالي للأفلام والمسلسلات

---

## 🔧 التفاصيل التقنية

### واجهة المستخدم الجديدة

#### الملف: `templates/index.html`
```html
<!-- General Statistics -->
<div id="generalStatsInfo" class="alert alert-success mt-3" style="display: none;">
    <h5><i class="fas fa-chart-pie"></i> الإحصائيات العامة</h5>
    <div class="row text-center mb-3">
        <div class="col-md-6">
            <div class="card border-success">
                <div class="card-body p-3">
                    <h6 class="card-title"><i class="fas fa-database"></i> إجمالي المستخرج</h6>
                    <h3><span id="totalExtractedCount" class="badge bg-success fs-4">0</span></h3>
                    <small class="text-muted">العدد الكامل المستخرج</small>
                    <div class="mt-2">
                        <button id="exportTotalCSVBtn" class="btn btn-success btn-sm me-1">
                            <i class="fas fa-file-csv"></i> CSV
                        </button>
                        <button id="exportTotalJSONBtn" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-file-code"></i> JSON
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card border-info">
                <div class="card-body p-3">
                    <h6 class="card-title"><i class="fas fa-eye"></i> المعروضة حالياً</h6>
                    <h3><span id="currentlyDisplayedCount" class="badge bg-info fs-4">0</span></h3>
                    <small class="text-muted">بعد تطبيق الفلاتر</small>
                    <div class="mt-2">
                        <button id="exportFilteredCSVBtn" class="btn btn-info btn-sm me-1">
                            <i class="fas fa-file-csv"></i> CSV
                        </button>
                        <button id="exportFilteredJSONBtn" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-file-code"></i> JSON
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filter Details (only shown when filters are applied) -->
    <div id="filterDetailsSection" class="row text-center" style="display: none;">
        <div class="col-md-12">
            <div class="alert alert-warning p-2">
                <strong><i class="fas fa-filter"></i> تفاصيل الفلترة: </strong>
                <span class="badge bg-danger me-2">
                    <i class="fas fa-times"></i> مستبعد: <span id="excludedByFilterCount">0</span>
                </span>
                <span class="badge bg-success">
                    <i class="fas fa-check"></i> متبقي: <span id="remainingAfterFilterCount">0</span>
                </span>
                <span class="mx-2">|</span>
                <small id="filterInfoText" class="text-muted"></small>
            </div>
        </div>
    </div>
</div>
```

### نظام البيانات المحسن

#### الملف: `static/script.js`
```javascript
// متغيرات عامة
let currentResults = [];
let totalExtractedResults = [];  // إجمالي المستخرج - يبقى حتى إغلاق التطبيق
let filteredResults = [];
let excludedResults = [];
let includedResults = [];

function displayResults(results, totalItems, moviesCount, seriesCount, isTemporaryView = false) {
    // حفظ النتائج في المتغيرات العامة (فقط إذا لم تكن عرض مؤقت)
    if (!isTemporaryView) {
        currentResults = results || [];
        
        // إضافة النتائج الجديدة إلى الإجمالي المستخرج (بدون تكرار)
        if (results && results.length > 0) {
            // إضافة النتائج الجديدة فقط
            const newResults = results.filter(newItem => 
                !totalExtractedResults.some(existingItem => 
                    existingItem.title === newItem.title && existingItem.link === newItem.link
                )
            );
            totalExtractedResults.push(...newResults);
        }
        
        filteredResults = [...currentResults];
        excludedResults = [];
        includedResults = [];
        
        // تحديث الإحصائيات العامة
        updateGeneralStats();
    }
}
```

### دالة الإحصائيات العامة

```javascript
function updateGeneralStats() {
    const generalStatsInfo = document.getElementById('generalStatsInfo');
    const totalExtractedCount = document.getElementById('totalExtractedCount');
    const currentlyDisplayedCount = document.getElementById('currentlyDisplayedCount');
    const filterDetailsSection = document.getElementById('filterDetailsSection');
    const excludedByFilterCount = document.getElementById('excludedByFilterCount');
    const remainingAfterFilterCount = document.getElementById('remainingAfterFilterCount');
    const filterInfoText = document.getElementById('filterInfoText');

    if (!generalStatsInfo) return;

    const totalExtracted = totalExtractedResults.length;
    const currentDisplayed = filteredResults.length;
    const excludedByFilter = excludedResults.length;
    const remainingAfterFilter = currentResults.length - excludedByFilter;

    // تحديث العدادات الرئيسية
    if (totalExtractedCount) totalExtractedCount.textContent = totalExtracted;
    if (currentlyDisplayedCount) currentlyDisplayedCount.textContent = currentDisplayed;

    // إظهار تفاصيل الفلترة إذا تم تطبيق فلاتر
    const hasFilters = excludedByFilter > 0 || currentFilterTerm;
    
    if (hasFilters && filterDetailsSection) {
        filterDetailsSection.style.display = 'block';
        
        if (excludedByFilterCount) excludedByFilterCount.textContent = excludedByFilter;
        if (remainingAfterFilterCount) remainingAfterFilterCount.textContent = remainingAfterFilter;
        
        // تحديث النص التوضيحي
        let infoText = '';
        if (currentFilterTerm) {
            infoText += `فلتر البحث: "${currentFilterTerm}"`;
        }
        
        if (!infoText) {
            infoText = 'تم تطبيق فلاتر';
        }
        
        if (filterInfoText) filterInfoText.textContent = infoText;
    } else if (filterDetailsSection) {
        filterDetailsSection.style.display = 'none';
    }

    // إظهار القسم
    generalStatsInfo.style.display = 'block';

    // تحديث حالة أزرار التصدير
    updateExportButtonsState(totalExtracted, currentDisplayed);
}
```

### نظام التصدير المحسن

```javascript
function exportResults(type, format = 'csv') {
    let dataToExport = [];
    let filename = '';
    
    switch(type) {
        case 'total':
            dataToExport = totalExtractedResults;
            filename = 'إجمالي_المستخرج';
            if (dataToExport.length === 0) {
                showAlert('لا توجد نتائج مستخرجة للتصدير', 'warning');
                return;
            }
            break;
        case 'filtered':
            dataToExport = filteredResults;
            filename = 'النتائج_المفلترة';
            if (dataToExport.length === 0) {
                showAlert('لا توجد نتائج مفلترة للتصدير', 'warning');
                return;
            }
            break;
    }
    
    if (format === 'json') {
        exportAsJSON(dataToExport, filename, type);
    } else {
        exportAsCSV(dataToExport, filename, type);
    }
}
```

---

## ✅ الميزات المحققة

### 1. **الإحصائيات العامة**:
- ✅ **إجمالي المستخرج**: يتراكم من جميع العمليات
- ✅ **عدم الحذف**: لا يتم حذفه عند الفلترة
- ✅ **البقاء حتى الإغلاق**: يبقى حتى إغلاق التطبيق أو تحديثه
- ✅ **تصدير مستقل**: أزرار CSV و JSON منفصلة

### 2. **المعروضة حالياً**:
- ✅ **بعد الفلترة**: العدد المعروض بعد تطبيق الفلاتر
- ✅ **تحديث فوري**: يتغير مع كل فلتر
- ✅ **تصدير مستقل**: أزرار CSV و JSON منفصلة

### 3. **تفاصيل الفلترة**:
- ✅ **إظهار عند الحاجة**: تظهر فقط عند تطبيق فلاتر
- ✅ **عدد المستبعد**: العناصر التي تم استبعادها بالفلتر
- ✅ **عدد المتبقي**: العناصر المتبقية بعد الفلتر
- ✅ **معلومات الفلتر**: نوع الفلتر المطبق

### 4. **تصدير JSON محسن**:
- ✅ **نفس الصيغة**: الهيكل الحالي للأفلام والمسلسلات
- ✅ **ملفات منفصلة**: عند وجود أفلام ومسلسلات معاً
- ✅ **أسماء واضحة**: `إجمالي_المستخرج_أفلام.json`

---

## 🧪 كيفية الاختبار

### اختبار الإحصائيات العامة:
1. افتح التطبيق: `http://localhost:5000`
2. أكمل عملية استخراج (مثلاً 50 عنصر)
3. **راقب**: إجمالي المستخرج = 50، المعروضة حالياً = 50
4. أكمل عملية استخراج أخرى (مثلاً 30 عنصر جديد)
5. **راقب**: إجمالي المستخرج = 80، المعروضة حالياً = 30

### اختبار عدم الحذف عند الفلترة:
1. بعد وجود 80 عنصر في الإجمالي
2. جرب فلترة بالاسم: أدخل "أكشن" واضغط "استبعاد بالاسم"
3. **راقب الإحصائيات**:
   - **إجمالي المستخرج**: 80 (لا يتغير)
   - **المعروضة حالياً**: 20 (بعد الفلترة)
   - **تفاصيل الفلترة**: مستبعد 10، متبقي 20

### اختبار التصدير:
1. اضغط "تصدير إجمالي المستخرج JSON"
   - **النتيجة**: ملف يحتوي على جميع الـ 80 عنصر
2. اضغط "تصدير المعروضة حالياً JSON"
   - **النتيجة**: ملف يحتوي على الـ 20 عنصر المفلتر فقط

### اختبار البقاء حتى الإغلاق:
1. بعد وجود 80 عنصر في الإجمالي
2. جرب فلاتر مختلفة، أعد تعيين الفلاتر، اعرض نتائج مختلفة
3. **راقب**: إجمالي المستخرج يبقى 80 دائماً
4. حدث الصفحة (F5)
5. **راقب**: إجمالي المستخرج يعود إلى 0 (تم المسح)

---

## 📊 مثال على سيناريو كامل

### السيناريو:
1. **العملية الأولى**: استخراج 50 عنصر (30 فيلم، 20 مسلسل)
2. **العملية الثانية**: استخراج 30 عنصر جديد (20 فيلم، 10 مسلسل)
3. **تطبيق فلتر**: استبعاد العناصر المحتوية على "أكشن" (15 عنصر)

### النتائج المتوقعة:
```
📊 الإحصائيات العامة:
├── إجمالي المستخرج: 80 عنصر (50 فيلم، 30 مسلسل)
├── المعروضة حالياً: 65 عنصر
└── تفاصيل الفلترة:
    ├── مستبعد: 15 عنصر
    ├── متبقي: 65 عنصر
    └── فلتر البحث: "أكشن"

🔽 أزرار التصدير:
├── تصدير إجمالي المستخرج CSV → 80 عنصر
├── تصدير إجمالي المستخرج JSON → ملفين (أفلام + مسلسلات)
├── تصدير المعروضة حالياً CSV → 65 عنصر
└── تصدير المعروضة حالياً JSON → ملفين (أفلام + مسلسلات مفلترة)
```

---

## 📚 الملفات المحدثة

### Frontend:
- `templates/index.html`: واجهة الإحصائيات العامة الجديدة
- `static/script.js`: نظام البيانات والإحصائيات المحسن

### Documentation:
- `GENERAL_STATISTICS_SYSTEM.md`: دليل شامل للنظام الجديد

---

## 🎉 الخلاصة

تم تطبيق النظام الجديد بنجاح مع الميزات التالية:

- ✅ **إحصائيات عامة**: إجمالي مستخرج + معروضة حالياً
- ✅ **عدم الحذف**: الإجمالي يبقى حتى إغلاق التطبيق
- ✅ **تفاصيل الفلترة**: تظهر عند تطبيق فلاتر
- ✅ **تصدير محسن**: أزرار منفصلة لكل نوع
- ✅ **نفس صيغة JSON**: الهيكل الحالي للأفلام والمسلسلات

**النظام الآن يوفر إحصائيات شاملة ومرنة مع حفظ البيانات! 🚀**

### للاختبار:
1. افتح `http://localhost:5000`
2. أكمل عدة عمليات استخراج
3. راقب تراكم الإجمالي المستخرج
4. جرب فلاتر مختلفة وراقب التفاصيل
5. جرب التصدير المنفصل لكل نوع

**جميع الميزات تعمل بشكل مثالي! 🎊**
