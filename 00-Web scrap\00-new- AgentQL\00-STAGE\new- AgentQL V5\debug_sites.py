#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup

def debug_site_structure(url, site_name):
    """تشخيص مفصل لبنية الموقع"""
    print(f"🔍 تشخيص {site_name}")
    print(f"🌐 الرابط: {url}")
    print("=" * 60)
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        print(f"✅ تم جلب الصفحة ({len(response.content)} بايت)")
        
        # 1. فحص جميع الروابط
        all_links = soup.find_all('a', href=True)
        print(f"\n📊 إجمالي الروابط: {len(all_links)}")
        
        # 2. فحص الروابط التي تحتوي على صور
        links_with_images = [link for link in all_links if link.find('img')]
        print(f"📊 روابط تحتوي على صور: {len(links_with_images)}")
        
        # 3. فحص الروابط التي تحتوي على كلمات مفتاحية
        movie_keywords = ['فيلم', 'movie', 'film', 'watch', 'مشاهدة']
        keyword_links = []
        for link in all_links:
            text = link.get_text().lower()
            href = link.get('href', '').lower()
            if any(keyword in text or keyword in href for keyword in movie_keywords):
                keyword_links.append(link)
        
        print(f"📊 روابط تحتوي على كلمات مفتاحية: {len(keyword_links)}")
        
        # 4. فحص جميع الصور
        all_images = soup.find_all('img')
        print(f"📊 إجمالي الصور: {len(all_images)}")
        
        # 5. عرض عينة من الروابط المحتملة
        print(f"\n🎬 عينة من الروابط المحتملة:")
        
        potential_links = links_with_images[:5] if links_with_images else keyword_links[:5]
        
        for i, link in enumerate(potential_links):
            print(f"\n--- رابط {i+1} ---")
            print(f"href: {link.get('href', 'لا يوجد')}")
            print(f"نص الرابط: {link.get_text(strip=True)[:100]}...")
            
            img = link.find('img')
            if img:
                print(f"صورة src: {img.get('src', 'لا يوجد')}")
                print(f"صورة alt: {img.get('alt', 'لا يوجد')}")
        
        # 6. فحص العناصر الأساسية
        print(f"\n🏗️ العناصر الأساسية:")
        
        # فحص divs مع classes
        divs_with_class = soup.find_all('div', class_=True)
        print(f"divs مع classes: {len(divs_with_class)}")
        
        # فحص articles
        articles = soup.find_all('article')
        print(f"articles: {len(articles)}")
        
        # فحص sections
        sections = soup.find_all('section')
        print(f"sections: {len(sections)}")
        
        # 7. فحص الـ classes الشائعة
        classes = {}
        for elem in soup.find_all(class_=True):
            for cls in elem.get('class', []):
                classes[cls] = classes.get(cls, 0) + 1
        
        print(f"\n🏷️ أكثر 15 class شيوعاً:")
        for cls, count in sorted(classes.items(), key=lambda x: x[1], reverse=True)[:15]:
            print(f"  .{cls}: {count}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    problem_sites = [
        ("https://a.asd.homes/category/foreign-movies-6/page/2/", "موقع ASD Homes"),
        ("https://vid.shahidwbas.tv/categories.php?cat=adult-movies&page=8&order=DESC", "موقع شاهد وبس")
    ]
    
    for url, site_name in problem_sites:
        debug_site_structure(url, site_name)
        print("\n" + "=" * 80 + "\n")

if __name__ == "__main__":
    main()
