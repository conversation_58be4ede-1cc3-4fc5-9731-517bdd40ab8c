#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لأداة استخراج بيانات الأفلام
"""

import sys
import os
import requests
import time
from scraper_classes import WebScraper, QueryParser

def test_query_parser():
    """اختبار محلل الاستعلامات"""
    print("🔍 اختبار محلل الاستعلامات...")
    
    parser = QueryParser()
    
    # اختبار الاستعلام الافتراضي
    query = "SELECT title, link, image FROM page"
    result = parser.parse_query(query)
    
    print(f"   الاستعلام: {query}")
    print(f"   الحقول: {result['fields']}")
    print(f"   المحدد: {result['selector']}")
    
    if result['fields'] == ['title', 'link', 'image']:
        print("   ✅ محلل الاستعلامات يعمل بشكل صحيح")
        return True
    else:
        print("   ❌ خطأ في محلل الاستعلامات")
        return False

def test_web_scraper():
    """اختبار مستخرج البيانات"""
    print("\n🕷️ اختبار مستخرج البيانات...")
    
    scraper = WebScraper()
    
    # اختبار على صفحة HTML بسيطة
    test_html = """
    <html>
    <body>
        <div class="movie-card">
            <h2>فيلم تجريبي</h2>
            <a href="/movie/1">رابط الفيلم</a>
            <img src="/poster1.jpg" alt="صورة الفيلم">
        </div>
        <div class="movie-item">
            <h3>فيلم آخر</h3>
            <a href="/movie/2">رابط آخر</a>
            <img src="/poster2.jpg" alt="صورة أخرى">
        </div>
    </body>
    </html>
    """
    
    # محاكاة استخراج البيانات
    from bs4 import BeautifulSoup
    soup = BeautifulSoup(test_html, 'html.parser')
    
    # اختبار استخراج العناصر
    elements = soup.find_all(['div'])
    print(f"   عدد العناصر الموجودة: {len(elements)}")
    
    if len(elements) >= 2:
        print("   ✅ مستخرج البيانات يعمل بشكل صحيح")
        return True
    else:
        print("   ❌ خطأ في مستخرج البيانات")
        return False

def test_app_connection():
    """اختبار الاتصال بالتطبيق"""
    print("\n🌐 اختبار الاتصال بالتطبيق...")
    
    try:
        # محاولة الاتصال بالتطبيق
        response = requests.get('http://127.0.0.1:5000', timeout=5)
        
        if response.status_code == 200:
            print("   ✅ التطبيق يعمل ويستجيب للطلبات")
            print(f"   كود الاستجابة: {response.status_code}")
            return True
        else:
            print(f"   ⚠️ التطبيق يستجيب لكن بكود: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ لا يمكن الاتصال بالتطبيق")
        print("   💡 تأكد من تشغيل التطبيق بـ: python run.py")
        return False
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
        return False

def test_scraping_api():
    """اختبار API الاستخراج"""
    print("\n📡 اختبار API الاستخراج...")
    
    try:
        # بيانات الاختبار
        test_data = {
            "url": "https://httpbin.org/html",  # موقع اختبار
            "query": "SELECT title, link, image FROM page",
            "content_type": "all",
            "element_type": "all",
            "max_pages": 1
        }
        
        response = requests.post(
            'http://127.0.0.1:5000/api/scrape',
            json=test_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ API يعمل بشكل صحيح")
                print(f"   عدد العناصر المستخرجة: {data.get('total_items', 0)}")
                return True
            else:
                print(f"   ⚠️ API يستجيب لكن هناك خطأ: {data.get('error')}")
                return False
        else:
            print(f"   ❌ خطأ في API: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار API: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🎬 اختبار أداة استخراج بيانات الأفلام")
    print("=" * 50)
    
    tests = [
        ("محلل الاستعلامات", test_query_parser),
        ("مستخرج البيانات", test_web_scraper),
        ("الاتصال بالتطبيق", test_app_connection),
        ("API الاستخراج", test_scraping_api)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   ❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام")
        print("🚀 يمكنك الآن فتح المتصفح والانتقال إلى: http://127.0.0.1:5000")
    elif passed >= total // 2:
        print("⚠️ معظم الاختبارات نجحت، التطبيق يعمل بشكل أساسي")
    else:
        print("❌ فشلت معظم الاختبارات، يرجى مراجعة الإعدادات")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
