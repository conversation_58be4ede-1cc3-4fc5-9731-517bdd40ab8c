<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحسينات الفلترة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #dee2e6;
        }

        .test-result {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }

        .btn {
            margin: 5px;
        }

        .collapsible-section {
            margin-bottom: 20px;
        }

        .collapsible-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .collapsible-header:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .collapsible-header h5 {
            margin: 0;
            color: #495057;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .toggle-icon {
            color: #6c757d;
            font-size: 1.2rem;
            transition: transform 0.3s ease;
        }

        .toggle-icon.rotated {
            transform: rotate(180deg);
        }

        .collapsible-content {
            background: white;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 10px 10px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .filter-card {
            background: white;
            border: 1px solid #e3e6ea;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .data-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }

        .item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 10px;
            margin: 5px 0;
        }

        .item.excluded {
            background: #f8d7da;
            border-color: #f5c6cb;
        }

        .item.included {
            background: #d4edda;
            border-color: #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">
            <i class="fas fa-test-tube"></i> اختبار تحسينات الفلترة
        </h1>

        <!-- اختبار القيمة الافتراضية -->
        <div class="test-section">
            <h3><i class="fas fa-hashtag"></i> اختبار القيمة الافتراضية لعدد العناصر</h3>
            <div class="row">
                <div class="col-md-6">
                    <label for="maxItemsPerFile">عدد العناصر لكل ملف:</label>
                    <input type="number" id="maxItemsPerFile" class="form-control" value="4000" min="1" max="10000">
                    <small class="form-text text-muted">سيتم فصل الأفلام والمسلسلات تلقائياً (افتراضي: 4000 عنصر)</small>
                </div>
                <div class="col-md-6">
                    <div class="test-result">
                        <strong>✅ النتيجة:</strong> القيمة الافتراضية أصبحت 4000 عنصر بدلاً من 100
                    </div>
                </div>
            </div>
        </div>

        <!-- اختبار الأقسام القابلة للطي -->
        <div class="test-section">
            <h3><i class="fas fa-expand-arrows-alt"></i> اختبار إجراءات الفلترة القابلة للطي</h3>
            
            <div class="filter-card collapsible-section">
                <div class="collapsible-header" onclick="toggleSection('filterActionsSection')">
                    <h5><i class="fas fa-cogs"></i> إجراءات الفلترة</h5>
                    <i class="fas fa-chevron-down toggle-icon" id="filterActionsSection-icon"></i>
                </div>
                <div class="collapsible-content" id="filterActionsSection-content" style="display: none;">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="btn-group-vertical d-grid gap-2">
                                <button id="resetFiltersBtn" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-undo"></i> إعادة تعيين جميع الفلاتر
                                </button>
                                <button id="showAllResultsBtn" class="btn btn-outline-primary btn-sm" onclick="testShowAllResults()">
                                    <i class="fas fa-list"></i> عرض جميع النتائج
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="btn-group-vertical d-grid gap-2">
                                <button id="showExcludedBtn" class="btn btn-outline-warning btn-sm" onclick="testShowExcluded()">
                                    <i class="fas fa-eye-slash"></i> عرض المستبعدة
                                </button>
                                <button id="showIncludedBtn" class="btn btn-outline-info btn-sm" onclick="testShowIncluded()">
                                    <i class="fas fa-eye"></i> عرض المختارة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="test-result">
                <strong>✅ النتيجة:</strong> قسم إجراءات الفلترة أصبح قابلاً للطي مع أنيميشن سلس
            </div>
        </div>

        <!-- اختبار وظائف العرض -->
        <div class="test-section">
            <h3><i class="fas fa-eye"></i> اختبار وظائف العرض المحدثة</h3>
            
            <!-- بيانات تجريبية -->
            <div class="row">
                <div class="col-md-4">
                    <h5>جميع النتائج الأصلية</h5>
                    <div class="data-display" id="allResults">
                        <div class="item">فيلم 1 - أكشن</div>
                        <div class="item">فيلم 2 - كوميديا</div>
                        <div class="item">فيلم 3 - رعب</div>
                        <div class="item">فيلم 4 - رومانسي</div>
                        <div class="item">فيلم 5 - خيال علمي</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <h5>المستبعدة</h5>
                    <div class="data-display" id="excludedResults">
                        <div class="item excluded">فيلم 3 - رعب</div>
                        <div class="item excluded">فيلم 5 - خيال علمي</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <h5>المختارة</h5>
                    <div class="data-display" id="includedResults">
                        <div class="item included">فيلم 1 - أكشن</div>
                        <div class="item included">فيلم 2 - كوميديا</div>
                        <div class="item included">فيلم 4 - رومانسي</div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-3">
                <button class="btn btn-primary" onclick="testShowAllResults()">
                    <i class="fas fa-list"></i> اختبار عرض جميع النتائج
                </button>
                <button class="btn btn-warning" onclick="testShowExcluded()">
                    <i class="fas fa-eye-slash"></i> اختبار عرض المستبعدة
                </button>
                <button class="btn btn-info" onclick="testShowIncluded()">
                    <i class="fas fa-eye"></i> اختبار عرض المختارة
                </button>
            </div>

            <div id="displayResult" class="test-result mt-3" style="display: none;">
                <strong>النتيجة:</strong> <span id="resultText"></span>
            </div>
        </div>

        <!-- ملخص التحسينات -->
        <div class="test-section">
            <h3><i class="fas fa-check-circle"></i> ملخص التحسينات المنجزة</h3>
            <ul class="list-group">
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    تغيير القيمة الافتراضية لعدد العناصر إلى 4000
                    <span class="badge bg-success rounded-pill">✅</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    جعل قسم إجراءات الفلترة قابلاً للطي
                    <span class="badge bg-success rounded-pill">✅</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    تحديث وظيفة عرض جميع النتائج لعرض البيانات الأصلية
                    <span class="badge bg-success rounded-pill">✅</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    تحديث وظيفة عرض المستبعدة لعرض العناصر المستبعدة فقط
                    <span class="badge bg-success rounded-pill">✅</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    تحديث وظيفة عرض المختارة لعرض العناصر المختارة فقط
                    <span class="badge bg-success rounded-pill">✅</span>
                </li>
            </ul>
        </div>
    </div>

    <script>
        // وظيفة التحكم في الأقسام القابلة للطي
        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId + '-content');
            const icon = document.getElementById(sectionId + '-icon');
            
            if (!content || !icon) return;
            
            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                icon.classList.add('rotated');
            } else {
                content.style.display = 'none';
                icon.classList.remove('rotated');
            }
        }

        // اختبار عرض جميع النتائج
        function testShowAllResults() {
            const resultDiv = document.getElementById('displayResult');
            const resultText = document.getElementById('resultText');
            
            resultText.textContent = 'تم عرض جميع النتائج الأصلية قبل الفلترة (5 عناصر)';
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result mt-3 alert alert-success';
            
            // تمييز جميع العناصر
            document.querySelectorAll('#allResults .item').forEach(item => {
                item.style.background = '#d4edda';
                item.style.borderColor = '#c3e6cb';
            });
        }

        // اختبار عرض المستبعدة
        function testShowExcluded() {
            const resultDiv = document.getElementById('displayResult');
            const resultText = document.getElementById('resultText');
            
            resultText.textContent = 'تم عرض العناصر المستبعدة فقط (2 عنصر)';
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result mt-3 alert alert-warning';
            
            // تمييز العناصر المستبعدة
            document.querySelectorAll('#excludedResults .item').forEach(item => {
                item.style.background = '#fff3cd';
                item.style.borderColor = '#ffeaa7';
            });
        }

        // اختبار عرض المختارة
        function testShowIncluded() {
            const resultDiv = document.getElementById('displayResult');
            const resultText = document.getElementById('resultText');
            
            resultText.textContent = 'تم عرض العناصر المختارة فقط (3 عناصر)';
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result mt-3 alert alert-info';
            
            // تمييز العناصر المختارة
            document.querySelectorAll('#includedResults .item').forEach(item => {
                item.style.background = '#d1ecf1';
                item.style.borderColor = '#bee5eb';
            });
        }
    </script>
</body>
</html>
