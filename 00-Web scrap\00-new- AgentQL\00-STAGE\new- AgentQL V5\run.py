#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل التطبيق
"""

import os
import sys

def check_requirements():
    """التحقق من المتطلبات"""
    try:
        import flask
        import requests
        import bs4
        import html5lib
        print("✅ جميع المكتبات المطلوبة متوفرة")
        return True
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("🔧 قم بتثبيت المتطلبات: pip install -r requirements.txt")
        return False

def check_files():
    """التحقق من الملفات المطلوبة"""
    required_files = [
        'app.py',
        'scraper_classes.py',
        'templates/index.html',
        'static/style.css',
        'static/script.js'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    else:
        print("✅ جميع الملفات المطلوبة موجودة")
        return True

def run_app():
    """تشغيل التطبيق"""
    print("🚀 بدء تشغيل التطبيق...")
    
    # التحقق من المتطلبات
    if not check_requirements():
        return False
    
    # التحقق من الملفات
    if not check_files():
        return False
    
    # تشغيل التطبيق
    try:
        from app import app
        print("✅ تم تحميل التطبيق بنجاح")
        print("🌐 التطبيق متاح على:")
        print("   - http://127.0.0.1:5000")
        print("   - http://localhost:5000")
        print("\n📝 للإيقاف: اضغط Ctrl+C")
        print("=" * 50)
        
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🕷️  أداة استخراج البيانات من المواقع")
    print("=" * 50)
    
    success = run_app()
    
    if not success:
        print("\n❌ فشل في تشغيل التطبيق")
        sys.exit(1)
    else:
        print("\n✅ تم إيقاف التطبيق بنجاح")
