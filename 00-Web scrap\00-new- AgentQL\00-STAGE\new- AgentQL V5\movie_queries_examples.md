# أمثلة على استعلامات البحث عن الأفلام

## 🎬 الاستعلام الافتراضي (الأكثر شمولية)

```sql
SELECT title, link, image FROM page WHERE class="movie-card"
```

**الوصف:** يبحث عن اسم الفيلم، رابط الفيلم، ورابط صورة الفيلم من العناصر التي تحتوي على class="movie-card"

---

## 🔍 استعلامات حسب أنواع المواقع المختلفة

### مواقع الأفلام العربية
```sql
SELECT title, link, image FROM page WHERE class="movie-item"
```

```sql
SELECT title, link, image FROM page WHERE class="film-card"
```

```sql
SELECT title, link, image FROM page WHERE class="movie-poster"
```

### مواقع الأفلام الأجنبية
```sql
SELECT title, link, image FROM page WHERE class="movie-box"
```

```sql
SELECT title, link, image FROM page WHERE class="film-item"
```

```sql
SELECT title, link, image FROM page WHERE class="movie-container"
```

### مواقع التورنت والتحميل
```sql
SELECT title, link, image FROM page WHERE class="torrent-item"
```

```sql
SELECT title, link, image FROM page WHERE class="download-item"
```

---

## 🎯 استعلامات متخصصة

### البحث عن الأفلام فقط (بدون مسلسلات)
```sql
SELECT title, link, image FROM page WHERE class="movie"
```

### البحث في عناصر محددة
```sql
SELECT title, link, image FROM page WHERE tag="article"
```

```sql
SELECT title, link, image FROM page WHERE tag="div"
```

### البحث بمعرف محدد
```sql
SELECT title, link, image FROM page WHERE id="movies-list"
```

---

## 🛠️ استعلامات للمواقع الشائعة

### نمط Bootstrap Cards
```sql
SELECT title, link, image FROM page WHERE class="card"
```

### نمط Grid Layout
```sql
SELECT title, link, image FROM page WHERE class="grid-item"
```

### نمط List Items
```sql
SELECT title, link, image FROM page WHERE class="list-item"
```

---

## 📝 نصائح لكتابة استعلامات فعالة

### 1. فحص الموقع أولاً
- افتح الموقع في المتصفح
- اضغط F12 لفتح أدوات المطور
- ابحث عن العناصر التي تحتوي على الأفلام
- لاحظ الـ class أو id المستخدم

### 2. جرب الاستعلام الافتراضي أولاً
```sql
SELECT title, link, image FROM page WHERE class="movie-card"
```

### 3. إذا لم يعمل، جرب هذه البدائل:
```sql
SELECT title, link, image FROM page WHERE class="movie"
SELECT title, link, image FROM page WHERE class="film"
SELECT title, link, image FROM page WHERE class="item"
SELECT title, link, image FROM page WHERE class="card"
```

### 4. للمواقع المعقدة، جرب:
```sql
SELECT title, link, image FROM page WHERE tag="article"
SELECT title, link, image FROM page WHERE tag="div"
```

---

## ⚡ استعلامات سريعة للاختبار

### اختبار سريع للعناوين فقط
```sql
SELECT title FROM page WHERE class="movie-card"
```

### اختبار سريع للروابط فقط
```sql
SELECT link FROM page WHERE class="movie-card"
```

### اختبار سريع للصور فقط
```sql
SELECT image FROM page WHERE class="movie-card"
```

---

## 🎪 أمثلة لمواقع حقيقية

### موقع IMDb
```sql
SELECT title, link, image FROM page WHERE class="titleColumn"
```

### مواقع الأفلام العربية
```sql
SELECT title, link, image FROM page WHERE class="movie_item"
```

```sql
SELECT title, link, image FROM page WHERE class="post-item"
```

### مواقع التحميل
```sql
SELECT title, link, image FROM page WHERE class="download-box"
```

---

## 🔧 استكشاف الأخطاء

### إذا لم تظهر نتائج:
1. جرب الاستعلام الافتراضي
2. استخدم مصحح الأخطاء لرؤية العناصر الموجودة
3. جرب class أو tag مختلف
4. تأكد من صحة رابط الموقع

### إذا ظهرت نتائج خاطئة:
1. كن أكثر تحديداً في الـ class
2. استخدم مصحح الأخطاء لفهم بنية الصفحة
3. جرب استعلام أبسط

---

**💡 نصيحة:** ابدأ دائماً بالاستعلام الافتراضي، ثم عدّل حسب بنية الموقع المستهدف!
