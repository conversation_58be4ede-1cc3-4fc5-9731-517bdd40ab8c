# دليل الفلترة المتقدمة للنتائج

## 🎯 نظرة عامة

تم إضافة نظام فلترة متقدم جديد يظهر **بعد انتهاء عملية البحث** ويوفر طرق متعددة لتصفية وتحليل النتائج بطريقة ذكية ومرنة.

---

## ✨ الميزات الجديدة

### 1. 🔤 **التصفية بالاسم**
- **دعم كلمات متعددة**: يمكن إدخال عدة كلمات مفصولة بفاصلة
- **البحث الذكي**: يبحث في أسماء الأفلام والمسلسلات
- **خيارات متنوعة**: استبعاد أو اختيار العناصر

### 2. 🖼️ **التصفية بالصور**
- **استبعاد العناصر بدون صور**: إزالة العناصر التي لا تحتوي على صور
- **كشف تلقائي**: يتعرف على العبارات التي تدل على عدم وجود صورة
- **دعم متعدد اللغات**: يدعم العربية والإنجليزية

### 3. 🔧 **إدارة الفلاتر**
- **إعادة تعيين شاملة**: مسح جميع الفلاتر بضغطة واحدة
- **إحصائيات مفصلة**: عرض تفاصيل النتائج المصفاة
- **تصدير مرن**: تصدير النتائج المصفاة بجميع التنسيقات

---

## 🚀 كيفية الاستخدام

### الخطوة 1: إنهاء عملية البحث
1. قم بتشغيل عملية استخراج البيانات كالمعتاد
2. انتظر حتى تكتمل العملية أو استخدم "إيقاف وتصدير"
3. ستظهر النتائج مع قسم **"فلترة وتصفية النتائج"**

### الخطوة 2: استخدام التصفية بالاسم

#### أ. التصفية بكلمة واحدة:
```
مثال: أكشن
النتيجة: سيتم العثور على جميع الأفلام المحتوية على كلمة "أكشن"
```

#### ب. التصفية بكلمات متعددة:
```
مثال: أكشن, كوميديا, رعب
النتيجة: سيتم العثور على الأفلام المحتوية على أي من هذه الكلمات
```

#### ج. خيارات التصفية:
- **استبعاد بالاسم**: إزالة الأفلام المحتوية على الكلمات المحددة
- **اختيار بالاسم**: عرض الأفلام المحتوية على الكلمات المحددة فقط

### الخطوة 3: استخدام التصفية بالصور

#### تفعيل الفلتر:
1. ضع علامة ✓ على "استبعاد العناصر التي لا توجد لها صورة"
2. اضغط "تطبيق فلتر الصور"

#### العبارات المكتشفة تلقائياً:
- "لا توجد صورة"
- "لا توجد لها صورة"
- "صورة غير متوفرة"
- "غير متوفرة"
- "No image"
- "N/A"
- حقول فارغة

### الخطوة 4: إدارة النتائج

#### أزرار العرض:
- **عرض جميع النتائج**: إظهار النتائج الأصلية
- **عرض المستبعدة**: إظهار العناصر المستبعدة فقط
- **عرض المختارة**: إظهار العناصر المختارة فقط

#### إعادة التعيين:
- **إعادة تعيين جميع الفلاتر**: مسح جميع التصفيات والعودة للنتائج الأصلية

#### التصدير:
- **تصدير النتائج المصفاة**: حفظ النتائج الحالية المعروضة

---

## 💡 أمثلة عملية

### مثال 1: البحث عن أفلام الأكشن فقط
```
1. أدخل "أكشن" في حقل البحث
2. اضغط "اختيار بالاسم"
3. النتيجة: عرض أفلام الأكشن فقط
```

### مثال 2: استبعاد أفلام الرعب والإثارة
```
1. أدخل "رعب, إثارة, مخيف" في حقل البحث
2. اضغط "استبعاد بالاسم"
3. النتيجة: عرض جميع الأفلام عدا أفلام الرعب والإثارة
```

### مثال 3: عرض الأفلام التي لها صور فقط
```
1. ضع علامة ✓ على "استبعاد العناصر التي لا توجد لها صورة"
2. اضغط "تطبيق فلتر الصور"
3. النتيجة: عرض الأفلام التي تحتوي على صور فقط
```

### مثال 4: فلترة مركبة
```
1. أدخل "كوميديا, رومانسية" واضغط "اختيار بالاسم"
2. ضع علامة ✓ على فلتر الصور واضغط "تطبيق فلتر الصور"
3. النتيجة: أفلام كوميدية ورومانسية لها صور فقط
```

---

## 📊 فهم الإحصائيات

### شريط المعلومات يعرض:
- **إجمالي النتائج**: العدد الكلي للعناصر المستخرجة
- **المعروضة حالياً**: عدد العناصر المعروضة بعد التصفية
- **المستبعدة**: عدد العناصر المستبعدة
- **المختارة**: عدد العناصر المختارة
- **كلمة البحث**: الكلمات المستخدمة في التصفية

### مثال على الإحصائيات:
```
📊 إجمالي النتائج: 100 | المعروضة حالياً: 25 | المستبعدة: 75 | المختارة: 25 | كلمة البحث: "أكشن, مغامرة"
```

---

## 🔧 نصائح متقدمة

### 1. **استخدام الكلمات المفتاحية بذكاء**
```
✅ جيد: "أكشن, مغامرة, إثارة"
❌ تجنب: "أكشن مغامرة إثارة" (بدون فواصل)
```

### 2. **الجمع بين أنواع التصفية**
- ابدأ بالتصفية بالاسم
- ثم طبق فلتر الصور
- استخدم أزرار العرض للتنقل

### 3. **التصدير الذكي**
- صفي النتائج أولاً
- تأكد من الإحصائيات
- ثم اضغط "تصدير النتائج المصفاة"

### 4. **إدارة النتائج الكبيرة**
- استخدم كلمات محددة للتصفية
- طبق فلتر الصور لتقليل العدد
- استخدم "عرض المختارة" للتركيز

---

## 🧪 اختبار الميزات

### اختبار سريع:
```bash
python test_advanced_filtering.py
```

### اختبار يدوي:
1. شغل التطبيق: `python app.py`
2. افتح المتصفح: `http://localhost:5000`
3. قم بعملية استخراج
4. جرب الفلاتر المختلفة

---

## ❓ استكشاف الأخطاء

### المشكلة: لا يظهر قسم الفلترة
**الحل**: تأكد من وجود نتائج بعد عملية البحث

### المشكلة: التصفية لا تعمل
**الحل**: 
- تأكد من إدخال كلمات صحيحة
- استخدم الفواصل بين الكلمات
- تحقق من وجود النتائج

### المشكلة: فلتر الصور لا يعمل
**الحل**:
- تأكد من وضع علامة ✓
- تحقق من وجود حقل الصور في النتائج

---

## 🎉 الخلاصة

نظام الفلترة المتقدم الجديد يوفر:
- **مرونة عالية** في تصفية النتائج
- **سهولة في الاستخدام** مع واجهة بديهية
- **كفاءة في الأداء** مع معالجة سريعة
- **تنوع في الخيارات** لتلبية جميع الاحتياجات

**استمتع بتجربة فلترة احترافية ومتقدمة! 🚀**
