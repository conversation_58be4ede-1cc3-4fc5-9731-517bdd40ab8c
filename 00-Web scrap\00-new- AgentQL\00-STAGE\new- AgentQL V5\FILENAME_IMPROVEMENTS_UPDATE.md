# تحديث أسماء ملفات JSON - إضافة اسم الموقع وعدد الأفلام

## 📋 ملخص التحديث

تم تحسين نظام تسمية ملفات JSON المُصدرة لتتضمن:
- **اسم الموقع**: يتم استخراجه تلقائياً من URL المُدخل
- **عدد العناصر**: عدد الأفلام أو المسلسلات في كل ملف

## 🎯 الهدف من التحديث

جعل أسماء الملفات أكثر وصفية ومفيدة للمستخدم، بحيث يمكن معرفة:
1. **مصدر البيانات**: من أي موقع تم استخراج البيانات
2. **حجم الملف**: كم عنصر يحتوي الملف
3. **نوع المحتوى**: أفلام أم مسلسلات

## 📁 أمثلة على أسماء الملفات الجديدة

### قبل التحديث:
```
movies_data_20241221_143022.json
series_data_20241221_143022.json
all_data_20241221_143022.json
```

### بعد التحديث:
```
egybest_أفلام_150عنصر_20241221_143022.json
mycima_مسلسلات_75عنصر_20241221_143022.json
shahid4u_جميع_البيانات_225عنصر_20241221_143022.json
```

## 🔧 التحديثات المُنجزة

### 1. JavaScript (static/script.js)

#### أ) وظيفة استخراج اسم الموقع:
```javascript
function extractSiteName(url) {
    // استخراج اسم الموقع من URL وتنظيفه
    // إزالة www. والنطاق العلوي
    // تنظيف الرموز الخاصة
}

function getCurrentSiteName() {
    // الحصول على اسم الموقع حسب وضع الاستخراج
    // (صفحة واحدة أو صفحات متتالية)
}
```

#### ب) تحديث وظائف التصدير:
- `exportAsJSON()`: تضمين اسم الموقع وعدد العناصر
- `createAndDownloadJSON()`: تحسين تسمية الملفات
- `exportResults()`: إرسال اسم الموقع للخادم

### 2. Python (scraper_classes.py)

#### أ) تحديث وظائف التصدير الرئيسية:
- `export_to_json()`: دعم معامل site_name
- `export_to_single_file()`: دعم معامل site_name
- `export_to_separate_files()`: دعم معامل site_name

#### ب) تحديث وظائف التصدير الفرعية:
- `_export_movies()`: تسمية محسنة للملفات
- `_export_series()`: تسمية محسنة للملفات
- `_export_single_json()`: تسمية محسنة للملفات
- `_export_single_csv()`: تسمية محسنة للملفات
- `_export_single_xlsx()`: تسمية محسنة للملفات

### 3. Flask API (app.py)

#### تحديث endpoint التصدير:
```python
@app.route('/api/export', methods=['POST'])
def export_data():
    # استقبال اسم الموقع من الطلب
    site_name = data.get('site_name', None)
    
    # تمرير اسم الموقع لوظائف التصدير
    files = exporter.export_to_json(results, max_items_per_file, site_name)
```

## 🎨 مميزات التحديث

### 1. **استخراج ذكي لاسم الموقع**
- إزالة تلقائية لـ `www.`
- إزالة النطاق العلوي (`.com`, `.net`, إلخ)
- تنظيف الرموز الخاصة لتكون مناسبة لأسماء الملفات
- دعم النصوص العربية والإنجليزية

### 2. **تسمية وصفية للملفات**
- تتضمن اسم الموقع المصدر
- تتضمن عدد العناصر في الملف
- تتضمن نوع المحتوى (أفلام/مسلسلات)
- تتضمن التاريخ والوقت

### 3. **دعم جميع أنواع التصدير**
- ملفات JSON منفصلة
- ملف JSON واحد مجمع
- ملفات CSV
- ملفات Excel (XLSX)

### 4. **رسائل محسنة**
- رسائل السجل تتضمن اسم الموقع
- رسائل النجاح أكثر وصفية
- معلومات أفضل للمستخدم

## 🔍 كيفية عمل النظام

### 1. **استخراج اسم الموقع**
```javascript
// مثال: https://www.egybest.com/movies
// النتيجة: egybest

const siteName = extractSiteName(url);
// egybest
```

### 2. **تكوين اسم الملف**
```javascript
// للأفلام
const filename = `${siteName}_أفلام_${moviesCount}عنصر`;
// egybest_أفلام_150عنصر

// للمسلسلات  
const filename = `${siteName}_مسلسلات_${seriesCount}عنصر`;
// egybest_مسلسلات_75عنصر
```

### 3. **إضافة التاريخ والامتداد**
```javascript
const finalFilename = `${filename}_${date}.json`;
// egybest_أفلام_150عنصر_20241221_143022.json
```

## 🧪 اختبار التحديثات

### اختبار استخراج اسم الموقع:
```javascript
console.log(extractSiteName('https://www.egybest.com')); // egybest
console.log(extractSiteName('https://mycima.tv')); // mycima
console.log(extractSiteName('https://shahid4u.cc')); // shahid4u
```

### اختبار تسمية الملفات:
1. قم بإدخال رابط موقع
2. ابدأ عملية الاستخراج
3. صدّر النتائج
4. تحقق من أسماء الملفات المُنتجة

## 📊 فوائد التحديث

### للمستخدم:
- **سهولة التنظيم**: معرفة مصدر كل ملف
- **معلومات سريعة**: معرفة حجم الملف دون فتحه
- **تصنيف أفضل**: تجميع الملفات حسب الموقع

### للمطور:
- **كود أكثر تنظيماً**: وظائف منفصلة ومحددة
- **سهولة الصيانة**: تحديثات مركزية
- **مرونة أكبر**: دعم مواقع جديدة بسهولة

## 🔄 التوافق مع الإصدارات السابقة

- النظام يعمل مع الملفات القديمة
- في حالة عدم توفر اسم الموقع، يستخدم "unknown_site"
- جميع الوظائف السابقة تعمل بشكل طبيعي

## 🚀 استخدام التحديثات

1. **أدخل رابط الموقع** في حقل URL
2. **ابدأ عملية الاستخراج** كالمعتاد
3. **صدّر النتائج** بأي طريقة مفضلة
4. **ستحصل على ملفات** بأسماء وصفية تتضمن اسم الموقع وعدد العناصر

## ✅ حالة التحديث

- [x] إضافة وظيفة استخراج اسم الموقع
- [x] تحديث وظائف التصدير في JavaScript
- [x] تحديث وظائف التصدير في Python
- [x] تحديث API endpoint للتصدير
- [x] اختبار وتوثيق التحديثات

**جميع التحديثات مكتملة وجاهزة للاستخدام! 🎉**
