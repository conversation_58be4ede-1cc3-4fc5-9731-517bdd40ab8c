/* تنسيق عام للتطبيق */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 20px;
}

/* تنسيق قسم تصفية النتائج المحدث */
.results-filter-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.results-filter-section h4 {
    color: #495057;
    margin-bottom: 25px;
    font-weight: 700;
    text-align: center;
    font-size: 1.4rem;
}

/* تنسيق بطاقات الفلترة */
.filter-card {
    background: white;
    border: 1px solid #e3e6ea;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.filter-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.filter-card h5 {
    color: #6c757d;
    margin-bottom: 15px;
    font-weight: 600;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
}

.filter-card h5 i {
    color: #007bff;
    margin-right: 8px;
}

/* تنسيق أزرار الفلترة */
.results-filter-section .btn {
    margin: 2px;
    transition: all 0.3s ease;
    font-weight: 500;
    border-radius: 8px;
}

.results-filter-section .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.results-filter-section .btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.results-filter-section .btn-outline-success:hover {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.results-filter-section .btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.results-filter-section .btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* تنسيق خاص للـ checkboxes */
.form-check {
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 5px 0;
}

.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.form-check-label {
    font-weight: 500;
    color: #495057;
}

.results-filter-section .btn-group-vertical .btn {
    margin-bottom: 5px;
}

.results-filter-section .btn-group .btn {
    margin-right: 5px;
}

/* تنسيق أزرار التحكم في العمليات */
.log-controls .btn {
    margin-right: 5px;
    margin-bottom: 5px;
    position: relative;
    z-index: 1000;
    pointer-events: auto !important;
    cursor: pointer !important;
}

.log-controls .btn:not(:disabled) {
    opacity: 1 !important;
    pointer-events: auto !important;
}

.log-controls .btn-outline-warning {
    border-color: #ffc107;
    color: #ffc107;
}

.log-controls .btn-outline-warning:hover {
    background-color: #ffc107;
    color: #212529;
}

.log-controls .btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545;
}

.log-controls .btn-outline-danger:hover {
    background-color: #dc3545;
    color: white;
}

/* ضمان أن الأزرار تبقى قابلة للتفاعل أثناء التحميل */
.loading .log-controls .btn {
    pointer-events: auto !important;
    opacity: 1 !important;
}

/* تنسيق خاص للأزرار النشطة */
.log-controls .btn.active {
    box-shadow: 0 0 10px rgba(0,123,255,0.5);
    transform: scale(1.05);
    transition: all 0.2s ease;
}

/* تأثيرات بصرية للإحصائيات المحدثة */
.stat-value.updated {
    animation: pulse-update 0.6s ease-in-out;
    color: #28a745;
    font-weight: bold;
}

@keyframes pulse-update {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* تنسيق معلومات التصفية */
#filterResultsInfo {
    border-left: 4px solid #17a2b8;
}

#filterResultsInfo small {
    font-size: 0.85em;
    opacity: 0.8;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    overflow: hidden;
}

.header {
    background: linear-gradient(45deg, #2c3e50, #3498db);
    color: white;
    padding: 30px;
    text-align: center;
}

.header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 300;
}

.header p {
    margin: 10px 0 0 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.main-content {
    padding: 30px;
}

.form-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 25px;
    border-left: 4px solid #3498db;
}

.form-section h3 {
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.btn-success {
    background: linear-gradient(45deg, #27ae60, #229954);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
}

.btn-warning {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
}

.results-section {
    margin-top: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #27ae60;
}

.results-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.results-table th,
.results-table td {
    padding: 15px;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.results-table th {
    background: linear-gradient(45deg, #34495e, #2c3e50);
    color: white;
    font-weight: 600;
}

.results-table tr:hover {
    background: #f1f3f4;
}

.debugger-section {
    margin-top: 30px;
    padding: 25px;
    background: #2c3e50;
    color: white;
    border-radius: 10px;
}

.debugger-section h3 {
    color: #ecf0f1;
    margin-top: 0;
}

.code-preview {
    background: #34495e;
    padding: 20px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    overflow-x: auto;
    margin: 15px 0;
}

.highlight {
    background: #f39c12;
    color: #2c3e50;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

.loading {
    text-align: center;
    padding: 40px;
    color: #7f8c8d;
}

.loading i {
    font-size: 2rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.alert {
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-danger {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: -10px;
}

.col-md-6 {
    flex: 0 0 50%;
    padding: 10px;
    box-sizing: border-box;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    padding: 10px;
    box-sizing: border-box;
}

.col-md-12 {
    flex: 0 0 100%;
    padding: 10px;
    box-sizing: border-box;
}

/* Progress Stats Styles */
.progress-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px 10px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.stat-value.updated {
    animation: pulse 0.6s ease;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Progress Bar Styles */
.progress-container {
    margin-top: 15px;
}

.progress-bar {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    height: 8px;
    overflow: hidden;
    position: relative;
    margin-bottom: 10px;
}

.progress-fill {
    background: linear-gradient(90deg, #4caf50, #8bc34a);
    height: 100%;
    border-radius: 25px;
    transition: width 0.5s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

#progressPercentage {
    font-weight: bold;
    font-size: 1.1rem;
}

#progressStatus {
    opacity: 0.9;
}

/* Progress Log Styles */
.progress-log-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    margin-top: 15px;
}

.progress-log {
    max-height: 300px;
    overflow-y: auto;
    padding: 15px;
    background: #ffffff;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.log-entry {
    margin-bottom: 8px;
    padding: 6px 10px;
    border-radius: 4px;
    border-left: 4px solid #007bff;
    background: #f8f9fa;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.log-entry.info {
    border-left-color: #17a2b8;
    background: #e7f3ff;
}

.log-entry.success {
    border-left-color: #28a745;
    background: #e8f5e8;
}

.log-entry.warning {
    border-left-color: #ffc107;
    background: #fff8e1;
}

.log-entry.error {
    border-left-color: #dc3545;
    background: #ffe6e6;
}

.log-entry .timestamp {
    color: #6c757d;
    font-weight: bold;
    white-space: nowrap;
    min-width: 80px;
}

.log-entry .message {
    flex: 1;
    word-wrap: break-word;
}

.log-controls {
    padding: 10px 15px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.progress-log::-webkit-scrollbar {
    width: 8px;
}

.progress-log::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.progress-log::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.progress-log::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Scraping Mode Selector Styles */
.scraping-mode-selector {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    padding: 15px;
    background: #ffffff;
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

.form-check-inline {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    border-radius: 6px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.form-check-inline:hover {
    background: #f8f9fa;
}

.form-check-input {
    margin: 0;
    cursor: pointer;
}

.form-check-label {
    margin: 0;
    cursor: pointer;
    font-weight: 500;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 5px;
}

.form-check-input:checked + .form-check-label {
    color: #007bff;
    font-weight: 600;
}

/* Export Settings Styles */
.btn-group .dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    border: none;
    padding: 8px 0;
}

.dropdown-item {
    padding: 10px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: #f8f9fa;
    color: #007bff;
}

.dropdown-item i {
    width: 16px;
    text-align: center;
}

/* Modal Styles */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    border-radius: 12px 12px 0 0;
    border-bottom: none;
}

.modal-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn-close {
    filter: invert(1);
}

/* Alert Info Styles */
.alert-info {
    background: #e7f3ff;
    border: 1px solid #b8daff;
    color: #004085;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.alert-info i {
    margin-right: 8px;
}

/* Required Field Indicator */
.text-danger {
    color: #dc3545 !important;
}

/* Enhanced Button Animations */
.log-controls .btn {
    transition: all 0.3s ease;
}

.log-controls .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Responsive Design Improvements */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .stat-item {
        padding: 10px 8px;
    }

    .stat-value {
        font-size: 1.5rem;
    }

    .log-controls {
        flex-direction: column;
        gap: 8px;
    }

    .log-controls .btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .progress-stats {
        padding: 15px;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .col-md-6,
    .col-md-4 {
        flex: 0 0 100%;
    }

    .header h1 {
        font-size: 2rem;
    }

    .main-content {
        padding: 20px;
    }

    .progress-log {
        font-size: 12px;
        max-height: 200px;
    }

    .log-controls {
        flex-direction: column;
        gap: 5px;
    }

    .scraping-mode-selector {
        flex-direction: column;
        gap: 10px;
    }

    .btn-group {
        width: 100%;
    }

    .btn-group .btn {
        width: 100%;
    }
}
