<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأقسام القابلة للطي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        /* تنسيق الأقسام القابلة للطي */
        .collapsible-section {
            margin-bottom: 20px;
        }

        .collapsible-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .collapsible-header:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .collapsible-header h3 {
            margin: 0;
            color: #495057;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .toggle-icon {
            color: #6c757d;
            font-size: 1.2rem;
            transition: transform 0.3s ease;
        }

        .toggle-icon.rotated {
            transform: rotate(180deg);
        }

        .collapsible-content {
            background: white;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 10px 10px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .collapsible-content.show {
            display: block !important;
            animation: slideDown 0.3s ease-out;
        }

        .collapsible-content.hide {
            animation: slideUp 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
                padding-top: 0;
                padding-bottom: 0;
            }
            to {
                opacity: 1;
                max-height: 500px;
                padding-top: 20px;
                padding-bottom: 20px;
            }
        }

        @keyframes slideUp {
            from {
                opacity: 1;
                max-height: 500px;
                padding-top: 20px;
                padding-bottom: 20px;
            }
            to {
                opacity: 0;
                max-height: 0;
                padding-top: 0;
                padding-bottom: 0;
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            }
            50% {
                transform: scale(1.02);
                box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            }
        }

        .test-content {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 10px 0;
        }

        .btn {
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">
            <i class="fas fa-test-tube"></i> اختبار الأقسام القابلة للطي
        </h1>

        <!-- قسم استعلام البحث -->
        <div class="collapsible-section">
            <div class="collapsible-header" onclick="toggleSection('querySection')">
                <h3><i class="fas fa-search"></i> استعلام البحث عن الأفلام</h3>
                <i class="fas fa-chevron-down toggle-icon" id="querySection-icon"></i>
            </div>
            <div class="collapsible-content" id="querySection-content" style="display: none;">
                <div class="test-content">
                    <h5>محتوى قسم استعلام البحث</h5>
                    <p>هذا هو محتوى قسم استعلام البحث. يمكن أن يحتوي على نماذج وحقول إدخال.</p>
                    <textarea class="form-control" rows="3" placeholder="SELECT title, link, image FROM page"></textarea>
                </div>
            </div>
        </div>

        <!-- قسم الفلاتر -->
        <div class="collapsible-section">
            <div class="collapsible-header" onclick="toggleSection('filtersSection')">
                <h3><i class="fas fa-filter"></i> الفلاتر</h3>
                <i class="fas fa-chevron-down toggle-icon" id="filtersSection-icon"></i>
            </div>
            <div class="collapsible-content" id="filtersSection-content" style="display: none;">
                <div class="test-content">
                    <h5>محتوى قسم الفلاتر</h5>
                    <p>هذا هو محتوى قسم الفلاتر. يمكن أن يحتوي على خيارات التصفية المختلفة.</p>
                    <div class="row">
                        <div class="col-md-6">
                            <label>عدد العناصر لكل ملف:</label>
                            <input type="number" class="form-control" value="100">
                        </div>
                        <div class="col-md-6">
                            <label>تأخير تحميل الصفحة:</label>
                            <input type="number" class="form-control" value="2">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الإحصائيات العامة للاختبار -->
        <div id="generalStatsInfo" class="alert alert-success mt-4">
            <h5><i class="fas fa-chart-pie"></i> الإحصائيات العامة</h5>
            <p>هذا هو قسم الإحصائيات العامة الذي سيتم الانتقال إليه تلقائياً عند التصفية.</p>
            <div class="text-center">
                <button class="btn btn-primary" onclick="testScrollToStats()">
                    <i class="fas fa-arrow-down"></i> اختبار الانتقال التلقائي
                </button>
            </div>
        </div>

        <!-- أزرار الاختبار -->
        <div class="text-center mt-4">
            <button class="btn btn-success" onclick="testFilterByName()">
                <i class="fas fa-text-width"></i> اختبار التصفية بالاسم
            </button>
            <button class="btn btn-warning" onclick="testImageFilter()">
                <i class="fas fa-image"></i> اختبار التصفية بالصور
            </button>
        </div>
    </div>

    <script>
        // وظيفة التحكم في الأقسام القابلة للطي
        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId + '-content');
            const icon = document.getElementById(sectionId + '-icon');
            
            if (!content || !icon) return;
            
            if (content.style.display === 'none' || content.style.display === '') {
                // إظهار القسم
                content.style.display = 'block';
                content.classList.add('show');
                content.classList.remove('hide');
                icon.classList.add('rotated');
            } else {
                // إخفاء القسم
                content.classList.add('hide');
                content.classList.remove('show');
                icon.classList.remove('rotated');
                
                // إخفاء القسم بعد انتهاء الأنيميشن
                setTimeout(() => {
                    if (content.classList.contains('hide')) {
                        content.style.display = 'none';
                    }
                }, 300);
            }
        }

        // وظيفة الانتقال إلى قسم الإحصائيات العامة
        function scrollToGeneralStats() {
            const generalStatsElement = document.getElementById('generalStatsInfo');
            if (generalStatsElement) {
                generalStatsElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
                
                // إضافة تأثير بصري للفت الانتباه
                generalStatsElement.style.animation = 'pulse 1s ease-in-out';
                setTimeout(() => {
                    generalStatsElement.style.animation = '';
                }, 1000);
            }
        }

        // اختبار التصفية بالاسم
        function testFilterByName() {
            alert('تم تطبيق التصفية بالاسم!');
            setTimeout(() => {
                scrollToGeneralStats();
            }, 500);
        }

        // اختبار التصفية بالصور
        function testImageFilter() {
            alert('تم تطبيق التصفية بالصور!');
            setTimeout(() => {
                scrollToGeneralStats();
            }, 500);
        }

        // اختبار الانتقال المباشر
        function testScrollToStats() {
            scrollToGeneralStats();
        }
    </script>
</body>
</html>
