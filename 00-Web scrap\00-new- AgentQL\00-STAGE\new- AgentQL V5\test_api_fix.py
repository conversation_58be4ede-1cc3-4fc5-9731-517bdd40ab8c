#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لإصلاح API
"""

import requests
import json
import time
import threading
from app import app

def start_server():
    """تشغيل الخادم في thread منفصل"""
    app.run(host='127.0.0.1', port=5000, debug=False)

def test_api():
    """اختبار API"""
    # انتظار تشغيل الخادم
    time.sleep(2)
    
    try:
        # اختبار الصفحة الرئيسية
        print("🧪 اختبار الصفحة الرئيسية...")
        response = requests.get('http://127.0.0.1:5000/')
        if response.status_code == 200:
            print("✅ الصفحة الرئيسية تعمل")
        else:
            print(f"❌ خطأ في الصفحة الرئيسية: {response.status_code}")
            return False
        
        # اختبار API scrape
        print("🧪 اختبار API scrape...")
        test_data = {
            "url": "https://httpbin.org/html",
            "query": "SELECT title, link, image FROM page",
            "max_pages": 1,
            "max_items_per_file": 100
        }
        
        response = requests.post(
            'http://127.0.0.1:5000/api/scrape',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ API scrape يعمل بشكل صحيح")
                print(f"📊 البيانات المستلمة: {len(data.get('data', []))} عنصر")
                return True
            else:
                print(f"❌ API scrape فشل: {data.get('error')}")
                return False
        else:
            print(f"❌ خطأ في API scrape: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

def main():
    """تشغيل الاختبار"""
    print("🚀 بدء اختبار إصلاح API")
    print("=" * 50)
    
    # تشغيل الخادم في thread منفصل
    server_thread = threading.Thread(target=start_server, daemon=True)
    server_thread.start()
    
    # تشغيل الاختبار
    success = test_api()
    
    if success:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ التطبيق جاهز للاستخدام")
        print("\n🔗 افتح المتصفح على: http://127.0.0.1:5000")
        print("📝 جرب إدخال رابط موقع والضغط على 'بدء الاستخراج'")
    else:
        print("\n❌ بعض الاختبارات فشلت")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    return success

if __name__ == "__main__":
    main()
