# تحديث الميزات المتقدمة - نظام الفلترة والعداد اللحظي

## 🎯 التحسينات المطبقة

### 1. **إصلاح العداد اللحظي أثناء المعالجة**
- ✅ **عداد مستمر**: يظهر التقدم الفعلي أثناء الاستخراج
- ✅ **تحديث تدريجي**: عرض النتائج تدريجياً مع تأثيرات بصرية
- ✅ **معلومات مفصلة**: عدد الأفلام، المسلسلات، الصفحة الحالية

### 2. **نظام فلترة متقدم مع عرض منفصل**
- ✅ **عرض المستبعدة**: مشاهدة النتائج المستبعدة بشكل منفصل
- ✅ **عرض المختارة**: مشاهدة النتائج المختارة بشكل منفصل
- ✅ **تصدير منفصل**: تصدير كل فئة بشكل مستقل

### 3. **إحصائيات مفصلة ومرئية**
- ✅ **عدادات رقمية**: إجمالي، مستبعدة، مختارة
- ✅ **أزرار ذكية**: تفعيل/تعطيل حسب وجود البيانات
- ✅ **معلومات فورية**: تحديث مستمر للإحصائيات

### 4. **عدم مسح النتائج بعد التصدير**
- ✅ **استمرارية البيانات**: النتائج تبقى متاحة بعد التصدير
- ✅ **مسح عند التحديث فقط**: البيانات تُمسح عند تحديث الصفحة
- ✅ **عرض مؤقت**: إمكانية عرض فئات مختلفة دون تأثير على البيانات الأساسية

---

## 🔧 التفاصيل التقنية

### إصلاح العداد اللحظي

#### الملف: `app.py`
```python
def scrape_single_page_with_progress(scraper, url, query, page_load_delay):
    """استخراج صفحة واحدة مع تحديث العداد اللحظي"""
    print(f"🔍 بدء استخراج صفحة واحدة: {url}")
    
    # تحديث الحالة - بدء العملية
    scraping_control['current_results'] = []
    scraping_control['summary'] = {'movies_count': 0, 'series_count': 0, 'total_count': 0}
    
    # استخراج البيانات
    results = scraper.scrape_website(url, query, max_pages=1, page_load_delay=page_load_delay)
    
    # تحديث النتائج تدريجياً لمحاكاة التقدم
    for i, item in enumerate(results):
        if scraping_control['should_stop']:
            break
            
        # تحديث العداد
        current_results = results[:i+1]
        movies = [item for item in current_results if item.get('type') == 'movie']
        series = [item for item in current_results if item.get('type') == 'series']
        
        scraping_control['current_results'] = current_results
        scraping_control['summary'] = {
            'movies_count': len(movies),
            'series_count': len(series),
            'total_count': len(current_results)
        }
        
        # تأخير قصير لإظهار التقدم
        time.sleep(0.5)
    
    return results

def scrape_multiple_pages_with_progress(scraper, pattern_info, query, max_pages, start_page, page_load_delay):
    """استخراج صفحات متعددة مع تحديث العداد اللحظي"""
    print(f"🔍 بدء استخراج {max_pages} صفحة من الصفحة {start_page}")
    
    # تحديث الحالة - بدء العملية
    scraping_control['current_results'] = []
    scraping_control['summary'] = {'movies_count': 0, 'series_count': 0, 'total_count': 0}
    
    all_results = []
    
    for page_num in range(start_page, start_page + max_pages):
        if scraping_control['should_stop']:
            break
            
        print(f"📄 معالجة الصفحة {page_num}")
        
        # بناء رابط الصفحة
        page_url = pattern_info['base_url'].replace('{page}', str(page_num))
        
        # استخراج الصفحة
        page_results = scraper.scrape_website(page_url, query, max_pages=1, page_load_delay=page_load_delay)
        all_results.extend(page_results)
        
        # تحديث العداد بعد كل صفحة
        movies = [item for item in all_results if item.get('type') == 'movie']
        series = [item for item in all_results if item.get('type') == 'series']
        
        scraping_control['current_results'] = all_results
        scraping_control['summary'] = {
            'movies_count': len(movies),
            'series_count': len(series),
            'total_count': len(all_results),
            'current_page': page_num,
            'total_pages': max_pages
        }
        
        print(f"✅ الصفحة {page_num}: {len(page_results)} عنصر جديد (الإجمالي: {len(all_results)})")
    
    return all_results, summary
```

### نظام الفلترة المتقدم

#### الملف: `templates/index.html`
```html
<!-- أزرار التصدير المنفصلة -->
<div class="row mt-2">
    <div class="col-md-4">
        <button id="exportAllBtn" class="btn btn-success btn-sm w-100">
            <i class="fas fa-download"></i> تصدير الكل
        </button>
    </div>
    <div class="col-md-4">
        <button id="exportExcludedBtn" class="btn btn-warning btn-sm w-100">
            <i class="fas fa-download"></i> تصدير المستبعدة
        </button>
    </div>
    <div class="col-md-4">
        <button id="exportIncludedBtn" class="btn btn-info btn-sm w-100">
            <i class="fas fa-download"></i> تصدير المختارة
        </button>
    </div>
</div>

<!-- إحصائيات مفصلة -->
<div class="row text-center">
    <div class="col-md-4">
        <h6><i class="fas fa-list"></i> إجمالي النتائج</h6>
        <span id="totalResultsCount" class="badge bg-primary fs-6">0</span>
    </div>
    <div class="col-md-4">
        <h6><i class="fas fa-eye-slash"></i> المستبعدة</h6>
        <span id="excludedResultsCount" class="badge bg-warning fs-6">0</span>
    </div>
    <div class="col-md-4">
        <h6><i class="fas fa-eye"></i> المختارة</h6>
        <span id="includedResultsCount" class="badge bg-info fs-6">0</span>
    </div>
</div>
```

#### الملف: `static/script.js`
```javascript
// دالة التصدير المحسنة
function exportResults(type) {
    let dataToExport = [];
    let filename = '';
    
    switch(type) {
        case 'all':
            dataToExport = currentResults;
            filename = 'جميع_النتائج';
            break;
        case 'excluded':
            dataToExport = excludedResults;
            filename = 'النتائج_المستبعدة';
            if (dataToExport.length === 0) {
                showAlert('لا توجد نتائج مستبعدة للتصدير', 'warning');
                return;
            }
            break;
        case 'included':
            dataToExport = includedResults;
            filename = 'النتائج_المختارة';
            if (dataToExport.length === 0) {
                showAlert('لا توجد نتائج مختارة للتصدير', 'warning');
                return;
            }
            break;
        default:
            dataToExport = filteredResults;
            filename = 'النتائج_المصفاة';
    }
    
    if (dataToExport.length === 0) {
        showAlert('لا توجد بيانات للتصدير', 'warning');
        return;
    }
    
    // إنشاء البيانات للتصدير
    const exportData = dataToExport.map((item, index) => ({
        'الرقم': index + 1,
        'اسم الفيلم': item.title || 'غير محدد',
        'رابط الفيلم': item.link || 'غير متوفر',
        'صورة الفيلم': item.image || 'غير متوفرة',
        'النوع': item.type === 'movie' ? 'فيلم' : 
                item.type === 'series' ? 'مسلسل' : 'غير محدد'
    }));
    
    // تحويل إلى CSV
    const csvContent = convertToCSV(exportData);
    
    // تحميل الملف
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // رسالة نجاح
    const typeText = type === 'all' ? 'جميع النتائج' : 
                    type === 'excluded' ? 'النتائج المستبعدة' : 
                    type === 'included' ? 'النتائج المختارة' : 'النتائج المصفاة';
    
    addLogEntry('success', `📥 تم تصدير ${typeText}: ${dataToExport.length} عنصر`);
    showAlert(`تم تصدير ${typeText} بنجاح (${dataToExport.length} عنصر)`, 'success');
}

// تحديث إحصائيات الفلترة المحسن
function updateFilterStats() {
    const filterInfo = document.getElementById('filterResultsInfo');
    const filterInfoText = document.getElementById('filterInfoText');
    const totalResultsCount = document.getElementById('totalResultsCount');
    const excludedResultsCount = document.getElementById('excludedResultsCount');
    const includedResultsCount = document.getElementById('includedResultsCount');

    if (!filterInfo) return;

    const totalResults = currentResults.length;
    const filteredCount = filteredResults.length;
    const excludedCount = excludedResults.length;
    const includedCount = includedResults.length;

    // تحديث العدادات الرقمية
    if (totalResultsCount) totalResultsCount.textContent = totalResults;
    if (excludedResultsCount) excludedResultsCount.textContent = excludedCount;
    if (includedResultsCount) includedResultsCount.textContent = includedCount;

    // تحديث النص التوضيحي
    let infoText = `المعروضة حالياً: ${filteredCount} عنصر`;
    if (currentFilterTerm) {
        infoText += ` | كلمة البحث: "${currentFilterTerm}"`;
    }
    
    if (filterInfoText) filterInfoText.textContent = infoText;
    filterInfo.style.display = 'block';

    // تحديث عدادات الأزرار
    const showExcludedBtn = document.getElementById('showExcludedBtn');
    const showIncludedBtn = document.getElementById('showIncludedBtn');
    const exportExcludedBtn = document.getElementById('exportExcludedBtn');
    const exportIncludedBtn = document.getElementById('exportIncludedBtn');

    if (showExcludedBtn) {
        showExcludedBtn.innerHTML = `<i class="fas fa-eye-slash"></i> عرض المستبعدة`;
        showExcludedBtn.disabled = excludedCount === 0;
    }

    if (showIncludedBtn) {
        showIncludedBtn.innerHTML = `<i class="fas fa-eye"></i> عرض المختارة`;
        showIncludedBtn.disabled = includedCount === 0;
    }

    if (exportExcludedBtn) {
        exportExcludedBtn.disabled = excludedCount === 0;
    }

    if (exportIncludedBtn) {
        exportIncludedBtn.disabled = includedCount === 0;
    }
}

// عرض النتائج مع دعم العرض المؤقت
function displayResults(results, totalItems, moviesCount, seriesCount, isTemporaryView = false) {
    const resultsSection = document.getElementById('resultsSection');
    const resultsTable = document.getElementById('resultsTable');
    
    // حفظ النتائج في المتغيرات العامة للفلترة (فقط إذا لم تكن عرض مؤقت)
    if (!isTemporaryView) {
        currentResults = results || [];
        filteredResults = [...currentResults];
        excludedResults = [];
        includedResults = [];
    }
    
    const resultsToDisplay = results || [];
    
    if (resultsToDisplay.length === 0) {
        resultsSection.innerHTML = '<p>لم يتم العثور على نتائج</p>';
        return;
    }
    
    // ... باقي كود العرض ...
    
    // إظهار قسم الفلترة وتفعيل الأزرار (فقط إذا لم تكن عرض مؤقت)
    const filterSection = document.querySelector('.results-filter-section');
    if (filterSection && !isTemporaryView && currentResults.length > 0) {
        filterSection.style.display = 'block';
        
        // تفعيل أزرار الفلترة
        setupFilterButtons();
        
        // تحديث إحصائيات الفلترة
        updateFilterStats();
        
        // إضافة رسالة في السجل
        addLogEntry('info', `📊 تم عرض ${currentResults.length} عنصر - الفلاتر متاحة الآن`);
    } else if (!isTemporaryView) {
        // تحديث الإحصائيات فقط
        updateFilterStats();
    }
}
```

---

## ✅ الميزات الجديدة

### 1. **العداد اللحظي المحسن**
- **تحديث مستمر**: يظهر التقدم الفعلي أثناء الاستخراج
- **معلومات مفصلة**: عدد الأفلام، المسلسلات، الصفحة الحالية
- **تأثيرات بصرية**: الأرقام تتحدث مع تأثير visual
- **شريط التقدم**: يتحرك ويظهر النسبة المئوية

### 2. **نظام الفلترة المتقدم**
- **عرض منفصل**: مشاهدة المستبعدة والمختارة بشكل منفصل
- **تصدير منفصل**: تصدير كل فئة بملف مستقل
- **إحصائيات مرئية**: عدادات رقمية واضحة
- **أزرار ذكية**: تفعيل/تعطيل حسب وجود البيانات

### 3. **استمرارية البيانات**
- **عدم المسح**: النتائج تبقى متاحة بعد التصدير
- **عرض مؤقت**: إمكانية عرض فئات مختلفة دون تأثير على البيانات
- **مسح عند التحديث**: البيانات تُمسح فقط عند تحديث الصفحة

### 4. **تحسينات واجهة المستخدم**
- **عدادات رقمية**: إجمالي، مستبعدة، مختارة
- **أزرار تصدير منفصلة**: لكل فئة زر تصدير خاص
- **رسائل واضحة**: تأكيدات وتحديثات فورية
- **تجربة سلسة**: لا انقطاع في العمل بعد التصدير

---

## 🧪 كيفية الاختبار

### اختبار العداد اللحظي:
1. ابدأ عملية استخراج لأي موقع
2. راقب العداد في الأعلى
3. **النتيجة المتوقعة**: أرقام تتحدث كل ثانية مع تأثيرات بصرية

### اختبار الفلترة المتقدمة:
1. أكمل عملية استخراج أو اضغط "إيقاف وتصدير"
2. جرب فلترة بالاسم: أدخل "أكشن" واضغط "استبعاد بالاسم"
3. اضغط "عرض المستبعدة" - ستشاهد النتائج المستبعدة فقط
4. اضغط "عرض جميع النتائج" - ستعود للعرض الأصلي
5. اضغط "تصدير المستبعدة" - سيتم تصدير المستبعدة فقط
6. **النتيجة**: النتائج الأصلية تبقى متاحة ولا تُمسح

### اختبار الإحصائيات:
1. راقب العدادات الرقمية في قسم الفلترة
2. جرب فلاتر مختلفة وراقب تحديث الأرقام
3. **النتيجة**: الأرقام تتحدث فوراً مع كل فلتر

---

## 🎉 الخلاصة

تم تطبيق جميع التحسينات المطلوبة بنجاح:

- ✅ **العداد اللحظي**: يعمل بشكل مستمر أثناء المعالجة
- ✅ **عرض المستبعدة**: متاح مع زر منفصل
- ✅ **عرض المختارة**: متاح مع زر منفصل
- ✅ **تصدير منفصل**: لكل فئة زر تصدير خاص
- ✅ **إحصائيات مفصلة**: إجمالي، مستبعدة، مختارة
- ✅ **عدم مسح النتائج**: تبقى متاحة بعد التصدير
- ✅ **مسح عند التحديث**: فقط عند تحديث الصفحة

**التطبيق الآن يوفر تجربة متقدمة ومتكاملة للفلترة والتصدير! 🚀**
