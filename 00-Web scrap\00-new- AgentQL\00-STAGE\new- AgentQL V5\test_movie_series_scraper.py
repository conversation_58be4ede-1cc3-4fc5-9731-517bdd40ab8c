#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لأداة استخراج بيانات الأفلام والمسلسلات
"""

import json
import os
import sys
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scraper_classes import WebScraper, DataExporter

def test_content_type_detection():
    """اختبار تحديد نوع المحتوى (فيلم أم مسلسل)"""
    print("🧪 اختبار تحديد نوع المحتوى...")
    
    scraper = WebScraper()
    
    # إنشاء عناصر وهمية للاختبار
    from bs4 import BeautifulSoup
    
    # اختبار فيلم
    movie_html = '<div class="movie-card"><h3>فيلم رائع 2024</h3><img src="poster.jpg"></div>'
    movie_soup = BeautifulSoup(movie_html, 'html.parser')
    movie_element = movie_soup.find('div')
    movie_type = scraper._detect_content_type(movie_element, "فيلم رائع 2024")
    
    # اختبار مسلسل
    series_html = '<div class="series-card"><h3>مسلسل مشوق - الموسم الأول</h3><img src="poster.jpg"></div>'
    series_soup = BeautifulSoup(series_html, 'html.parser')
    series_element = series_soup.find('div')
    series_type = scraper._detect_content_type(series_element, "مسلسل مشوق - الموسم الأول")
    
    print(f"   ✅ تحديد الفيلم: {movie_type}")
    print(f"   ✅ تحديد المسلسل: {series_type}")
    
    return movie_type == 'movie' and series_type == 'series'

def test_data_extraction():
    """اختبار استخراج البيانات بالصيغة الجديدة"""
    print("🧪 اختبار استخراج البيانات...")
    
    scraper = WebScraper()
    
    # إنشاء بيانات وهمية
    from bs4 import BeautifulSoup
    
    # بيانات فيلم
    movie_html = '''
    <div class="movie-card">
        <a href="/movie/123">
            <img src="movie-poster.jpg" alt="فيلم رائع 2024">
            <h3>فيلم رائع 2024</h3>
        </a>
    </div>
    '''
    
    # بيانات مسلسل
    series_html = '''
    <div class="series-card">
        <a href="/series/456">
            <img src="series-poster.jpg" alt="مسلسل مشوق">
            <h3>مسلسل مشوق - الموسم الأول</h3>
        </a>
    </div>
    '''
    
    movie_soup = BeautifulSoup(movie_html, 'html.parser')
    series_soup = BeautifulSoup(series_html, 'html.parser')
    
    movie_element = movie_soup.find('div')
    series_element = series_soup.find('div')
    
    # استخراج البيانات
    movie_data = scraper._extract_movie_data(movie_element, ['title', 'link', 'image'], 'https://example.com')
    series_data = scraper._extract_movie_data(series_element, ['title', 'link', 'image'], 'https://example.com')
    
    print(f"   📽️ بيانات الفيلم: {movie_data}")
    print(f"   📺 بيانات المسلسل: {series_data}")
    
    # التحقق من الصيغة الصحيحة
    movie_valid = (
        'movies_name' in movie_data and 
        'movies_img' in movie_data and 
        'movies_href' in movie_data and
        movie_data.get('type') == 'movie'
    )
    
    series_valid = (
        'series_name' in series_data and 
        'series_img' in series_data and 
        'series_href' in series_data and
        series_data.get('type') == 'series'
    )
    
    return movie_valid and series_valid

def test_data_export():
    """اختبار تصدير البيانات بالصيغة الجديدة"""
    print("🧪 اختبار تصدير البيانات...")
    
    # إنشاء بيانات وهمية
    test_data = [
        {
            'type': 'movie',
            'movies_name': 'فيلم رائع 2024',
            'movies_img': 'https://example.com/movie1.jpg',
            'movies_href': 'https://example.com/movie/1',
            'title': 'فيلم رائع 2024',
            'imageUrl': 'https://example.com/movie1.jpg',
            'link': 'https://example.com/movie/1'
        },
        {
            'type': 'movie',
            'movies_name': 'فيلم آخر 2024',
            'movies_img': 'https://example.com/movie2.jpg',
            'movies_href': 'https://example.com/movie/2',
            'title': 'فيلم آخر 2024',
            'imageUrl': 'https://example.com/movie2.jpg',
            'link': 'https://example.com/movie/2'
        },
        {
            'type': 'series',
            'series_name': 'مسلسل مشوق',
            'series_img': 'https://example.com/series1.jpg',
            'series_href': 'https://example.com/series/1',
            'title': 'مسلسل مشوق',
            'imageUrl': 'https://example.com/series1.jpg',
            'link': 'https://example.com/series/1'
        }
    ]
    
    # إنشاء مجلد اختبار
    test_export_folder = 'test_exports'
    if not os.path.exists(test_export_folder):
        os.makedirs(test_export_folder)
    
    # تصدير البيانات
    exporter = DataExporter(test_export_folder)
    files = exporter.export_to_json(test_data, max_items_per_file=10)
    
    print(f"   📁 تم إنشاء {len(files)} ملف")
    
    # التحقق من الملفات
    valid_files = 0
    for filename in files:
        filepath = os.path.join(test_export_folder, filename)
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            if 'movies_data' in filename:
                if 'movies_info' in data:
                    print(f"   ✅ ملف الأفلام صحيح: {filename}")
                    print(f"      📽️ عدد الأفلام: {len(data['movies_info'])}")
                    valid_files += 1
                    
            elif 'series_data' in filename:
                if 'series_info' in data:
                    print(f"   ✅ ملف المسلسلات صحيح: {filename}")
                    print(f"      📺 عدد المسلسلات: {len(data['series_info'])}")
                    valid_files += 1
    
    # تنظيف ملفات الاختبار
    for filename in files:
        filepath = os.path.join(test_export_folder, filename)
        if os.path.exists(filepath):
            os.remove(filepath)
    
    if os.path.exists(test_export_folder):
        os.rmdir(test_export_folder)
    
    return valid_files == len(files)

def test_json_structure():
    """اختبار هيكل ملفات JSON"""
    print("🧪 اختبار هيكل ملفات JSON...")
    
    # إنشاء بيانات اختبار
    movie_data = {
        'type': 'movie',
        'movies_name': 'فيلم اختبار',
        'movies_img': 'https://example.com/test.jpg',
        'movies_href': 'https://example.com/test'
    }
    
    series_data = {
        'type': 'series',
        'series_name': 'مسلسل اختبار',
        'series_img': 'https://example.com/test.jpg',
        'series_href': 'https://example.com/test'
    }
    
    # اختبار هيكل الأفلام
    expected_movie_structure = {
        "movies_info": [
            {
                "movies_name": "فيلم اختبار",
                "movies_img": "https://example.com/test.jpg",
                "movies_href": "https://example.com/test"
            }
        ]
    }
    
    # اختبار هيكل المسلسلات
    expected_series_structure = {
        "series_info": [
            {
                "series_name": "مسلسل اختبار",
                "series_img": "https://example.com/test.jpg",
                "series_href": "https://example.com/test"
            }
        ]
    }
    
    print("   ✅ هيكل الأفلام:")
    print(f"      {json.dumps(expected_movie_structure, ensure_ascii=False, indent=2)}")
    
    print("   ✅ هيكل المسلسلات:")
    print(f"      {json.dumps(expected_series_structure, ensure_ascii=False, indent=2)}")
    
    return True

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار أداة استخراج الأفلام والمسلسلات")
    print("=" * 60)
    
    tests = [
        ("تحديد نوع المحتوى", test_content_type_detection),
        ("استخراج البيانات", test_data_extraction),
        ("تصدير البيانات", test_data_export),
        ("هيكل JSON", test_json_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n📋 {test_name}:")
            result = test_func()
            if result:
                print(f"   ✅ نجح الاختبار: {test_name}")
                passed += 1
            else:
                print(f"   ❌ فشل الاختبار: {test_name}")
        except Exception as e:
            print(f"   ❌ خطأ في الاختبار {test_name}: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام")
        print("\n🎬 المميزات الجديدة:")
        print("   • تحديد تلقائي لنوع المحتوى (فيلم/مسلسل)")
        print("   • تصدير منفصل للأفلام والمسلسلات")
        print("   • هيكل JSON محسن حسب المطلوب")
        print("   • دعم أفضل لبطاقات الأفلام والمسلسلات")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
