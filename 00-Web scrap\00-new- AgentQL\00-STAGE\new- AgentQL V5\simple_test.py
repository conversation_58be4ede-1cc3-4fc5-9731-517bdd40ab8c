#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للتطبيق
"""

import time
import requests

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    print("🔍 اختبار الوظائف الأساسية...")
    
    # اختبار محلل الاستعلامات
    try:
        from scraper_classes import QueryParser
        parser = QueryParser()
        result = parser.parse_query("SELECT title, link FROM page WHERE class='test'")
        print("✅ محلل الاستعلامات يعمل")
        print(f"   الحقول: {result['fields']}")
        print(f"   المحدد: {result['selector']}")
    except Exception as e:
        print(f"❌ خطأ في محلل الاستعلامات: {e}")
    
    # اختبار محلل أنماط الصفحات
    try:
        from scraper_classes import PagePatternAnalyzer
        analyzer = PagePatternAnalyzer()
        result = analyzer.analyze_pattern([
            "https://example.com/page/1",
            "https://example.com/page/2",
            "https://example.com/page/3"
        ])
        print("✅ محلل أنماط الصفحات يعمل")
        print(f"   النمط: {result.get('pattern')}")
    except Exception as e:
        print(f"❌ خطأ في محلل أنماط الصفحات: {e}")
    
    # اختبار مصدر البيانات
    try:
        from scraper_classes import DataExporter
        exporter = DataExporter()
        test_data = [
            {"title": "اختبار 1", "link": "http://test1.com", "image": "http://test1.jpg"},
            {"title": "اختبار 2", "link": "http://test2.com", "image": "http://test2.jpg"}
        ]
        files = exporter.export_to_json(test_data, "movies", 100)
        print("✅ مصدر البيانات يعمل")
        print(f"   الملفات المصدرة: {files}")
    except Exception as e:
        print(f"❌ خطأ في مصدر البيانات: {e}")

def test_app_connection():
    """اختبار الاتصال بالتطبيق"""
    print("\n🌐 اختبار الاتصال بالتطبيق...")
    
    try:
        # انتظار قصير للتأكد من تشغيل التطبيق
        time.sleep(2)
        
        response = requests.get("http://127.0.0.1:5000", timeout=5)
        if response.status_code == 200:
            print("✅ التطبيق يعمل ويستجيب للطلبات")
            print(f"   كود الاستجابة: {response.status_code}")
            return True
        else:
            print(f"⚠️  التطبيق يستجيب لكن بكود غير متوقع: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالتطبيق")
        print("   تأكد من أن التطبيق يعمل على المنفذ 5000")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء الاختبار البسيط...")
    print("=" * 50)
    
    # اختبار الوظائف الأساسية
    test_basic_functionality()
    
    # اختبار الاتصال
    app_running = test_app_connection()
    
    print("\n" + "=" * 50)
    if app_running:
        print("🎉 التطبيق جاهز للاستخدام!")
        print("📱 افتح المتصفح وانتقل إلى: http://127.0.0.1:5000")
    else:
        print("⚠️  التطبيق قد لا يعمل بشكل صحيح")
        print("🔧 تحقق من تشغيل التطبيق بالأمر: python app.py")
